namespace WinFormsApp1
{
    partial class frmDeTaiChiTiet
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            panelHeader = new Panel();
            groupBoxInfo = new GroupBox();
            lblDeTaiList = new Label();
            cmbDeTaiList = new ComboBox();
            btnThemDeTai = new Button();
            btnSuaDeTai = new Button();
            btnXoaDeTai = new Button();
            lblMaDeTai = new Label();
            txtMaDeTai = new TextBox();
            lblTenDeTai = new Label();
            txtTenDeTai = new TextBox();
            lblMoTa = new Label();
            txtMoTa = new TextBox();
            lblLinhVuc = new Label();
            txtLinhVuc = new TextBox();
            lblCapQuanLy = new Label();
            cmbCapQuanLy = new ComboBox();
            lblThoiGianBatDau = new Label();
            dtpThoiGianBatDau = new DateTimePicker();
            lblThoiGianKetThuc = new Label();
            dtpThoiGianKetThuc = new DateTimePicker();
            tabControl = new TabControl();
            tabThanhVien = new TabPage();
            dgvThanhVien = new DataGridView();
            dataGridViewTextBoxColumn1 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn2 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn3 = new DataGridViewTextBoxColumn();
            panelThanhVienButtons = new Panel();
            btnThemThanhVien = new Button();
            btnXoaThanhVien = new Button();
            tabDonVi = new TabPage();
            dgvDonVi = new DataGridView();
            dataGridViewTextBoxColumn4 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn5 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn6 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn7 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn8 = new DataGridViewTextBoxColumn();
            panelDonViButtons = new Panel();
            btnThemDonVi = new Button();
            btnSuaDonVi = new Button();
            btnXoaDonVi = new Button();
            tabKinhPhi = new TabPage();
            groupBoxKinhPhi = new GroupBox();
            btnLuu = new Button();
            lblKinhPhiNganSach = new Label();
            txtKinhPhiNganSach = new TextBox();
            lblKinhPhiKhac = new Label();
            txtKinhPhiKhac = new TextBox();
            lblTongKinhPhi = new Label();
            txtTongKinhPhi = new TextBox();
            tabSanPhamI = new TabPage();
            splitContainerSanPhamI = new SplitContainer();
            dgvSanPhamI = new DataGridView();
            dataGridViewTextBoxColumn9 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn10 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn11 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn12 = new DataGridViewTextBoxColumn();
            panelSanPhamIButtons = new Panel();
            btnThemSanPhamI = new Button();
            btnSuaSanPhamI = new Button();
            btnXoaSanPhamI = new Button();
            groupBoxDacTinh = new GroupBox();
            dgvDacTinhKyThuat = new DataGridView();
            dataGridViewTextBoxColumn13 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn14 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn15 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn16 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn17 = new DataGridViewTextBoxColumn();
            panelDacTinhButtons = new Panel();
            btnThemDacTinh = new Button();
            btnSuaDacTinh = new Button();
            btnXoaDacTinh = new Button();
            tabSanPhamII = new TabPage();
            dgvSanPhamII = new DataGridView();
            dataGridViewTextBoxColumn18 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn19 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn20 = new DataGridViewTextBoxColumn();
            panelSanPhamIIButtons = new Panel();
            btnThemSanPhamII = new Button();
            btnSuaSanPhamII = new Button();
            btnXoaSanPhamII = new Button();
            tabSanPhamIII = new TabPage();
            dgvSanPhamIII = new DataGridView();
            dataGridViewTextBoxColumn21 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn22 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn23 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn24 = new DataGridViewTextBoxColumn();
            panelSanPhamIIIButtons = new Panel();
            btnThemSanPhamIII = new Button();
            btnSuaSanPhamIII = new Button();
            btnXoaSanPhamIII = new Button();
            panelHeader.SuspendLayout();
            groupBoxInfo.SuspendLayout();
            tabControl.SuspendLayout();
            tabThanhVien.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvThanhVien).BeginInit();
            panelThanhVienButtons.SuspendLayout();
            tabDonVi.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvDonVi).BeginInit();
            panelDonViButtons.SuspendLayout();
            tabKinhPhi.SuspendLayout();
            groupBoxKinhPhi.SuspendLayout();
            tabSanPhamI.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)splitContainerSanPhamI).BeginInit();
            splitContainerSanPhamI.Panel1.SuspendLayout();
            splitContainerSanPhamI.Panel2.SuspendLayout();
            splitContainerSanPhamI.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvSanPhamI).BeginInit();
            panelSanPhamIButtons.SuspendLayout();
            groupBoxDacTinh.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvDacTinhKyThuat).BeginInit();
            panelDacTinhButtons.SuspendLayout();
            tabSanPhamII.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvSanPhamII).BeginInit();
            panelSanPhamIIButtons.SuspendLayout();
            tabSanPhamIII.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvSanPhamIII).BeginInit();
            panelSanPhamIIIButtons.SuspendLayout();
            SuspendLayout();
            //
            // panelHeader
            //
            panelHeader.Controls.Add(groupBoxInfo);
            panelHeader.Dock = DockStyle.Top;
            panelHeader.Location = new Point(0, 0);
            panelHeader.Name = "panelHeader";
            panelHeader.Padding = new Padding(10);
            panelHeader.Size = new Size(1200, 216);
            panelHeader.TabIndex = 12;
            //
            // groupBoxInfo
            //
            groupBoxInfo.Controls.Add(lblDeTaiList);
            groupBoxInfo.Controls.Add(cmbDeTaiList);
            groupBoxInfo.Controls.Add(btnThemDeTai);
            groupBoxInfo.Controls.Add(btnSuaDeTai);
            groupBoxInfo.Controls.Add(btnXoaDeTai);
            groupBoxInfo.Controls.Add(lblMaDeTai);
            groupBoxInfo.Controls.Add(txtMaDeTai);
            groupBoxInfo.Controls.Add(lblTenDeTai);
            groupBoxInfo.Controls.Add(txtTenDeTai);
            groupBoxInfo.Controls.Add(lblMoTa);
            groupBoxInfo.Controls.Add(txtMoTa);
            groupBoxInfo.Controls.Add(lblLinhVuc);
            groupBoxInfo.Controls.Add(txtLinhVuc);
            groupBoxInfo.Controls.Add(lblCapQuanLy);
            groupBoxInfo.Controls.Add(cmbCapQuanLy);
            groupBoxInfo.Controls.Add(lblThoiGianBatDau);
            groupBoxInfo.Controls.Add(dtpThoiGianBatDau);
            groupBoxInfo.Controls.Add(lblThoiGianKetThuc);
            groupBoxInfo.Controls.Add(dtpThoiGianKetThuc);
            groupBoxInfo.Dock = DockStyle.Fill;
            groupBoxInfo.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            groupBoxInfo.Location = new Point(10, 10);
            groupBoxInfo.Name = "groupBoxInfo";
            groupBoxInfo.Size = new Size(1180, 196);
            groupBoxInfo.TabIndex = 0;
            groupBoxInfo.TabStop = false;
            groupBoxInfo.Text = "Thông tin cơ bản đề tài";
            //
            // lblDeTaiList
            //
            lblDeTaiList.Location = new Point(20, 30);
            lblDeTaiList.Name = "lblDeTaiList";
            lblDeTaiList.Size = new Size(80, 20);
            lblDeTaiList.TabIndex = 0;
            lblDeTaiList.Text = "Chọn đề tài:";
            //
            // cmbDeTaiList
            //
            cmbDeTaiList.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbDeTaiList.Location = new Point(20, 50);
            cmbDeTaiList.Name = "cmbDeTaiList";
            cmbDeTaiList.Size = new Size(400, 25);
            cmbDeTaiList.TabIndex = 0;
            cmbDeTaiList.SelectedIndexChanged += CmbDeTaiList_SelectedIndexChanged;
            //
            // btnThemDeTai
            //
            btnThemDeTai.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnThemDeTai.BackColor = Color.FromArgb(40, 167, 69);
            btnThemDeTai.ForeColor = Color.White;
            btnThemDeTai.Location = new Point(1000, 30);
            btnThemDeTai.Name = "btnThemDeTai";
            btnThemDeTai.Size = new Size(157, 41);
            btnThemDeTai.TabIndex = 1;
            btnThemDeTai.Text = "➕ Thêm";
            btnThemDeTai.UseVisualStyleBackColor = false;
            btnThemDeTai.Click += BtnThemDeTai_Click;
            //
            // btnSuaDeTai
            //
            btnSuaDeTai.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnSuaDeTai.BackColor = Color.FromArgb(255, 193, 7);
            btnSuaDeTai.ForeColor = Color.Black;
            btnSuaDeTai.Location = new Point(1000, 77);
            btnSuaDeTai.Name = "btnSuaDeTai";
            btnSuaDeTai.Size = new Size(157, 41);
            btnSuaDeTai.TabIndex = 2;
            btnSuaDeTai.Text = "✏️ Sửa";
            btnSuaDeTai.UseVisualStyleBackColor = false;
            btnSuaDeTai.Click += BtnSuaDeTai_Click;
            //
            // btnXoaDeTai
            //
            btnXoaDeTai.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnXoaDeTai.BackColor = Color.FromArgb(220, 53, 69);
            btnXoaDeTai.ForeColor = Color.White;
            btnXoaDeTai.Location = new Point(1000, 124);
            btnXoaDeTai.Name = "btnXoaDeTai";
            btnXoaDeTai.Size = new Size(157, 41);
            btnXoaDeTai.TabIndex = 3;
            btnXoaDeTai.Text = "❌ Xóa";
            btnXoaDeTai.UseVisualStyleBackColor = false;
            btnXoaDeTai.Click += BtnXoaDeTai_Click;
            //
            // lblMaDeTai
            //
            lblMaDeTai.Location = new Point(450, 30);
            lblMaDeTai.Name = "lblMaDeTai";
            lblMaDeTai.Size = new Size(80, 20);
            lblMaDeTai.TabIndex = 4;
            lblMaDeTai.Text = "Mã đề tài:";
            //
            // txtMaDeTai
            //
            txtMaDeTai.Location = new Point(450, 50);
            txtMaDeTai.Name = "txtMaDeTai";
            txtMaDeTai.ReadOnly = true;
            txtMaDeTai.Size = new Size(100, 25);
            txtMaDeTai.TabIndex = 4;
            //
            // lblTenDeTai
            //
            lblTenDeTai.Location = new Point(20, 80);
            lblTenDeTai.Name = "lblTenDeTai";
            lblTenDeTai.Size = new Size(80, 20);
            lblTenDeTai.TabIndex = 5;
            lblTenDeTai.Text = "Tên đề tài:";
            //
            // txtTenDeTai
            //
            txtTenDeTai.Location = new Point(20, 100);
            txtTenDeTai.Name = "txtTenDeTai";
            txtTenDeTai.ReadOnly = true;
            txtTenDeTai.Size = new Size(530, 25);
            txtTenDeTai.TabIndex = 5;
            //
            // lblMoTa
            //
            lblMoTa.Location = new Point(580, 30);
            lblMoTa.Name = "lblMoTa";
            lblMoTa.Size = new Size(80, 20);
            lblMoTa.TabIndex = 6;
            lblMoTa.Text = "Mô tả:";
            //
            // txtMoTa
            //
            txtMoTa.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            txtMoTa.Location = new Point(580, 50);
            txtMoTa.Multiline = true;
            txtMoTa.Name = "txtMoTa";
            txtMoTa.ReadOnly = true;
            txtMoTa.ScrollBars = ScrollBars.Vertical;
            txtMoTa.Size = new Size(400, 70);
            txtMoTa.TabIndex = 6;
            //
            // lblLinhVuc
            //
            lblLinhVuc.Location = new Point(20, 130);
            lblLinhVuc.Name = "lblLinhVuc";
            lblLinhVuc.Size = new Size(80, 20);
            lblLinhVuc.TabIndex = 7;
            lblLinhVuc.Text = "Lĩnh vực:";
            //
            // txtLinhVuc
            //
            txtLinhVuc.Location = new Point(20, 150);
            txtLinhVuc.Name = "txtLinhVuc";
            txtLinhVuc.ReadOnly = true;
            txtLinhVuc.Size = new Size(200, 25);
            txtLinhVuc.TabIndex = 7;
            //
            // lblCapQuanLy
            //
            lblCapQuanLy.Location = new Point(250, 130);
            lblCapQuanLy.Name = "lblCapQuanLy";
            lblCapQuanLy.Size = new Size(80, 20);
            lblCapQuanLy.TabIndex = 8;
            lblCapQuanLy.Text = "Cấp quản lý:";
            //
            // cmbCapQuanLy
            //
            cmbCapQuanLy.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbCapQuanLy.Enabled = false;
            cmbCapQuanLy.Items.AddRange(new object[] { "Nhà nước", "Bộ", "Ngành", "Cơ sở" });
            cmbCapQuanLy.Location = new Point(250, 150);
            cmbCapQuanLy.Name = "cmbCapQuanLy";
            cmbCapQuanLy.Size = new Size(150, 25);
            cmbCapQuanLy.TabIndex = 8;
            //
            // lblThoiGianBatDau
            //
            lblThoiGianBatDau.Location = new Point(430, 130);
            lblThoiGianBatDau.Name = "lblThoiGianBatDau";
            lblThoiGianBatDau.Size = new Size(120, 20);
            lblThoiGianBatDau.TabIndex = 9;
            lblThoiGianBatDau.Text = "Thời gian bắt đầu:";
            //
            // dtpThoiGianBatDau
            //
            dtpThoiGianBatDau.Enabled = false;
            dtpThoiGianBatDau.Format = DateTimePickerFormat.Short;
            dtpThoiGianBatDau.Location = new Point(430, 150);
            dtpThoiGianBatDau.Name = "dtpThoiGianBatDau";
            dtpThoiGianBatDau.Size = new Size(120, 25);
            dtpThoiGianBatDau.TabIndex = 9;
            //
            // lblThoiGianKetThuc
            //
            lblThoiGianKetThuc.Location = new Point(570, 130);
            lblThoiGianKetThuc.Name = "lblThoiGianKetThuc";
            lblThoiGianKetThuc.Size = new Size(120, 20);
            lblThoiGianKetThuc.TabIndex = 10;
            lblThoiGianKetThuc.Text = "Thời gian kết thúc:";
            //
            // dtpThoiGianKetThuc
            //
            dtpThoiGianKetThuc.Enabled = false;
            dtpThoiGianKetThuc.Format = DateTimePickerFormat.Short;
            dtpThoiGianKetThuc.Location = new Point(570, 150);
            dtpThoiGianKetThuc.Name = "dtpThoiGianKetThuc";
            dtpThoiGianKetThuc.Size = new Size(120, 25);
            dtpThoiGianKetThuc.TabIndex = 10;
            //
            // tabControl
            //
            tabControl.Controls.Add(tabThanhVien);
            tabControl.Controls.Add(tabDonVi);
            tabControl.Controls.Add(tabKinhPhi);
            tabControl.Controls.Add(tabSanPhamI);
            tabControl.Controls.Add(tabSanPhamII);
            tabControl.Controls.Add(tabSanPhamIII);
            tabControl.Dock = DockStyle.Fill;
            tabControl.Font = new Font("Segoe UI", 10F);
            tabControl.Location = new Point(0, 216);
            tabControl.Name = "tabControl";
            tabControl.SelectedIndex = 0;
            tabControl.Size = new Size(1200, 484);
            tabControl.TabIndex = 11;
            //
            // tabThanhVien
            //
            tabThanhVien.Controls.Add(dgvThanhVien);
            tabThanhVien.Controls.Add(panelThanhVienButtons);
            tabThanhVien.Location = new Point(4, 26);
            tabThanhVien.Name = "tabThanhVien";
            tabThanhVien.Size = new Size(1192, 454);
            tabThanhVien.TabIndex = 0;
            tabThanhVien.Text = "👥 Thành viên tham gia";
            tabThanhVien.UseVisualStyleBackColor = true;
            //
            // dgvThanhVien
            //
            dgvThanhVien.AllowUserToAddRows = false;
            dgvThanhVien.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgvThanhVien.Columns.AddRange(new DataGridViewColumn[] { dataGridViewTextBoxColumn1, dataGridViewTextBoxColumn2, dataGridViewTextBoxColumn3 });
            dgvThanhVien.Dock = DockStyle.Fill;
            dgvThanhVien.Location = new Point(0, 0);
            dgvThanhVien.Name = "dgvThanhVien";
            dgvThanhVien.ReadOnly = true;
            dgvThanhVien.Size = new Size(1192, 414);
            dgvThanhVien.TabIndex = 0;
            //
            // dataGridViewTextBoxColumn1
            //
            dataGridViewTextBoxColumn1.HeaderText = "STT";
            dataGridViewTextBoxColumn1.Name = "dataGridViewTextBoxColumn1";
            dataGridViewTextBoxColumn1.ReadOnly = true;
            //
            // dataGridViewTextBoxColumn2
            //
            dataGridViewTextBoxColumn2.HeaderText = "Họ tên";
            dataGridViewTextBoxColumn2.Name = "dataGridViewTextBoxColumn2";
            dataGridViewTextBoxColumn2.ReadOnly = true;
            //
            // dataGridViewTextBoxColumn3
            //
            dataGridViewTextBoxColumn3.HeaderText = "Vai trò";
            dataGridViewTextBoxColumn3.Name = "dataGridViewTextBoxColumn3";
            dataGridViewTextBoxColumn3.ReadOnly = true;
            //
            // panelThanhVienButtons
            //
            panelThanhVienButtons.Controls.Add(btnThemThanhVien);
            panelThanhVienButtons.Controls.Add(btnXoaThanhVien);
            panelThanhVienButtons.Dock = DockStyle.Bottom;
            panelThanhVienButtons.Location = new Point(0, 414);
            panelThanhVienButtons.Name = "panelThanhVienButtons";
            panelThanhVienButtons.Size = new Size(1192, 40);
            panelThanhVienButtons.TabIndex = 1;
            //
            // btnThemThanhVien
            //
            btnThemThanhVien.BackColor = Color.FromArgb(40, 167, 69);
            btnThemThanhVien.ForeColor = Color.White;
            btnThemThanhVien.Location = new Point(10, 5);
            btnThemThanhVien.Name = "btnThemThanhVien";
            btnThemThanhVien.Size = new Size(100, 30);
            btnThemThanhVien.TabIndex = 0;
            btnThemThanhVien.Text = "➕ Thêm";
            btnThemThanhVien.UseVisualStyleBackColor = false;
            btnThemThanhVien.Click += BtnThemThanhVien_Click;
            //
            // btnXoaThanhVien
            //
            btnXoaThanhVien.BackColor = Color.FromArgb(220, 53, 69);
            btnXoaThanhVien.ForeColor = Color.White;
            btnXoaThanhVien.Location = new Point(120, 5);
            btnXoaThanhVien.Name = "btnXoaThanhVien";
            btnXoaThanhVien.Size = new Size(100, 30);
            btnXoaThanhVien.TabIndex = 1;
            btnXoaThanhVien.Text = "❌ Xóa";
            btnXoaThanhVien.UseVisualStyleBackColor = false;
            btnXoaThanhVien.Click += BtnXoaThanhVien_Click;
            //
            // tabDonVi
            //
            tabDonVi.Controls.Add(dgvDonVi);
            tabDonVi.Controls.Add(panelDonViButtons);
            tabDonVi.Location = new Point(4, 26);
            tabDonVi.Name = "tabDonVi";
            tabDonVi.Size = new Size(1192, 470);
            tabDonVi.TabIndex = 1;
            tabDonVi.Text = "🏢 Đơn vị phối hợp";
            tabDonVi.UseVisualStyleBackColor = true;
            //
            // dgvDonVi
            //
            dgvDonVi.AllowUserToAddRows = false;
            dgvDonVi.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgvDonVi.Columns.AddRange(new DataGridViewColumn[] { dataGridViewTextBoxColumn4, dataGridViewTextBoxColumn5, dataGridViewTextBoxColumn6, dataGridViewTextBoxColumn7, dataGridViewTextBoxColumn8 });
            dgvDonVi.Dock = DockStyle.Fill;
            dgvDonVi.Location = new Point(0, 0);
            dgvDonVi.Name = "dgvDonVi";
            dgvDonVi.ReadOnly = true;
            dgvDonVi.Size = new Size(1192, 430);
            dgvDonVi.TabIndex = 0;
            //
            // dataGridViewTextBoxColumn4
            //
            dataGridViewTextBoxColumn4.HeaderText = "STT";
            dataGridViewTextBoxColumn4.Name = "dataGridViewTextBoxColumn4";
            dataGridViewTextBoxColumn4.ReadOnly = true;
            dataGridViewTextBoxColumn4.Width = 40;
            //
            // dataGridViewTextBoxColumn5
            //
            dataGridViewTextBoxColumn5.HeaderText = "Tên đơn vị";
            dataGridViewTextBoxColumn5.Name = "dataGridViewTextBoxColumn5";
            dataGridViewTextBoxColumn5.ReadOnly = true;
            //
            // dataGridViewTextBoxColumn6
            //
            dataGridViewTextBoxColumn6.HeaderText = "Địa chỉ";
            dataGridViewTextBoxColumn6.Name = "dataGridViewTextBoxColumn6";
            dataGridViewTextBoxColumn6.ReadOnly = true;
            //
            // dataGridViewTextBoxColumn7
            //
            dataGridViewTextBoxColumn7.HeaderText = "Điện thoại";
            dataGridViewTextBoxColumn7.Name = "dataGridViewTextBoxColumn7";
            dataGridViewTextBoxColumn7.ReadOnly = true;
            //
            // dataGridViewTextBoxColumn8
            //
            dataGridViewTextBoxColumn8.HeaderText = "Email";
            dataGridViewTextBoxColumn8.Name = "dataGridViewTextBoxColumn8";
            dataGridViewTextBoxColumn8.ReadOnly = true;
            //
            // panelDonViButtons
            //
            panelDonViButtons.Controls.Add(btnThemDonVi);
            panelDonViButtons.Controls.Add(btnSuaDonVi);
            panelDonViButtons.Controls.Add(btnXoaDonVi);
            panelDonViButtons.Dock = DockStyle.Bottom;
            panelDonViButtons.Location = new Point(0, 430);
            panelDonViButtons.Name = "panelDonViButtons";
            panelDonViButtons.Size = new Size(1192, 40);
            panelDonViButtons.TabIndex = 1;
            //
            // btnThemDonVi
            //
            btnThemDonVi.BackColor = Color.FromArgb(40, 167, 69);
            btnThemDonVi.ForeColor = Color.White;
            btnThemDonVi.Location = new Point(10, 5);
            btnThemDonVi.Name = "btnThemDonVi";
            btnThemDonVi.Size = new Size(100, 30);
            btnThemDonVi.TabIndex = 0;
            btnThemDonVi.Text = "➕ Thêm";
            btnThemDonVi.UseVisualStyleBackColor = false;
            btnThemDonVi.Click += BtnThemDonVi_Click;
            //
            // btnSuaDonVi
            //
            btnSuaDonVi.BackColor = Color.FromArgb(255, 193, 7);
            btnSuaDonVi.ForeColor = Color.Black;
            btnSuaDonVi.Location = new Point(120, 5);
            btnSuaDonVi.Name = "btnSuaDonVi";
            btnSuaDonVi.Size = new Size(100, 30);
            btnSuaDonVi.TabIndex = 1;
            btnSuaDonVi.Text = "✏️ Sửa";
            btnSuaDonVi.UseVisualStyleBackColor = false;
            btnSuaDonVi.Click += BtnSuaDonVi_Click;
            //
            // btnXoaDonVi
            //
            btnXoaDonVi.BackColor = Color.FromArgb(220, 53, 69);
            btnXoaDonVi.ForeColor = Color.White;
            btnXoaDonVi.Location = new Point(230, 5);
            btnXoaDonVi.Name = "btnXoaDonVi";
            btnXoaDonVi.Size = new Size(100, 30);
            btnXoaDonVi.TabIndex = 2;
            btnXoaDonVi.Text = "❌ Xóa";
            btnXoaDonVi.UseVisualStyleBackColor = false;
            btnXoaDonVi.Click += BtnXoaDonVi_Click;
            //
            // tabKinhPhi
            //
            tabKinhPhi.Controls.Add(groupBoxKinhPhi);
            tabKinhPhi.Location = new Point(4, 26);
            tabKinhPhi.Name = "tabKinhPhi";
            tabKinhPhi.Size = new Size(1192, 470);
            tabKinhPhi.TabIndex = 2;
            tabKinhPhi.Text = "💰 Kinh phí";
            tabKinhPhi.UseVisualStyleBackColor = true;
            //
            // groupBoxKinhPhi
            //
            groupBoxKinhPhi.Controls.Add(btnLuu);
            groupBoxKinhPhi.Controls.Add(lblKinhPhiNganSach);
            groupBoxKinhPhi.Controls.Add(txtKinhPhiNganSach);
            groupBoxKinhPhi.Controls.Add(lblKinhPhiKhac);
            groupBoxKinhPhi.Controls.Add(txtKinhPhiKhac);
            groupBoxKinhPhi.Controls.Add(lblTongKinhPhi);
            groupBoxKinhPhi.Controls.Add(txtTongKinhPhi);
            groupBoxKinhPhi.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            groupBoxKinhPhi.Location = new Point(20, 20);
            groupBoxKinhPhi.Name = "groupBoxKinhPhi";
            groupBoxKinhPhi.Size = new Size(400, 226);
            groupBoxKinhPhi.TabIndex = 0;
            groupBoxKinhPhi.TabStop = false;
            groupBoxKinhPhi.Text = "Thông tin kinh phí";
            //
            // btnLuu
            //
            btnLuu.BackColor = Color.FromArgb(40, 167, 69);
            btnLuu.ForeColor = Color.White;
            btnLuu.Location = new Point(20, 179);
            btnLuu.Name = "btnLuu";
            btnLuu.Size = new Size(80, 30);
            btnLuu.TabIndex = 0;
            btnLuu.Text = "💾 Lưu";
            btnLuu.UseVisualStyleBackColor = false;
            btnLuu.Click += BtnLuu_Click;
            //
            // lblKinhPhiNganSach
            //
            lblKinhPhiNganSach.Location = new Point(20, 30);
            lblKinhPhiNganSach.Name = "lblKinhPhiNganSach";
            lblKinhPhiNganSach.Size = new Size(150, 20);
            lblKinhPhiNganSach.TabIndex = 0;
            lblKinhPhiNganSach.Text = "Kinh phí ngân sách (VNĐ):";
            //
            // txtKinhPhiNganSach
            //
            txtKinhPhiNganSach.Location = new Point(20, 50);
            txtKinhPhiNganSach.Name = "txtKinhPhiNganSach";
            txtKinhPhiNganSach.Size = new Size(200, 25);
            txtKinhPhiNganSach.TabIndex = 0;
            //
            // lblKinhPhiKhac
            //
            lblKinhPhiKhac.Location = new Point(20, 80);
            lblKinhPhiKhac.Name = "lblKinhPhiKhac";
            lblKinhPhiKhac.Size = new Size(150, 20);
            lblKinhPhiKhac.TabIndex = 1;
            lblKinhPhiKhac.Text = "Kinh phí khác (VNĐ):";
            //
            // txtKinhPhiKhac
            //
            txtKinhPhiKhac.Location = new Point(20, 100);
            txtKinhPhiKhac.Name = "txtKinhPhiKhac";
            txtKinhPhiKhac.Size = new Size(200, 25);
            txtKinhPhiKhac.TabIndex = 1;
            //
            // lblTongKinhPhi
            //
            lblTongKinhPhi.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            lblTongKinhPhi.ForeColor = Color.Red;
            lblTongKinhPhi.Location = new Point(20, 130);
            lblTongKinhPhi.Name = "lblTongKinhPhi";
            lblTongKinhPhi.Size = new Size(150, 20);
            lblTongKinhPhi.TabIndex = 2;
            lblTongKinhPhi.Text = "Tổng kinh phí (VNĐ):";
            //
            // txtTongKinhPhi
            //
            txtTongKinhPhi.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            txtTongKinhPhi.ForeColor = Color.Red;
            txtTongKinhPhi.Location = new Point(20, 150);
            txtTongKinhPhi.Name = "txtTongKinhPhi";
            txtTongKinhPhi.ReadOnly = true;
            txtTongKinhPhi.Size = new Size(200, 23);
            txtTongKinhPhi.TabIndex = 2;
            //
            // tabSanPhamI
            //
            tabSanPhamI.Controls.Add(splitContainerSanPhamI);
            tabSanPhamI.Location = new Point(4, 26);
            tabSanPhamI.Name = "tabSanPhamI";
            tabSanPhamI.Size = new Size(1192, 470);
            tabSanPhamI.TabIndex = 3;
            tabSanPhamI.Text = "🔧 Sản phẩm Dạng I";
            tabSanPhamI.UseVisualStyleBackColor = true;
            //
            // splitContainerSanPhamI
            //
            splitContainerSanPhamI.Dock = DockStyle.Fill;
            splitContainerSanPhamI.Location = new Point(0, 0);
            splitContainerSanPhamI.Name = "splitContainerSanPhamI";
            splitContainerSanPhamI.Orientation = Orientation.Horizontal;
            //
            // splitContainerSanPhamI.Panel1
            //
            splitContainerSanPhamI.Panel1.Controls.Add(dgvSanPhamI);
            splitContainerSanPhamI.Panel1.Controls.Add(panelSanPhamIButtons);
            //
            // splitContainerSanPhamI.Panel2
            //
            splitContainerSanPhamI.Panel2.Controls.Add(groupBoxDacTinh);
            splitContainerSanPhamI.Size = new Size(1192, 470);
            splitContainerSanPhamI.SplitterDistance = 275;
            splitContainerSanPhamI.TabIndex = 0;
            //
            // dgvSanPhamI
            //
            dgvSanPhamI.AllowUserToAddRows = false;
            dgvSanPhamI.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgvSanPhamI.Columns.AddRange(new DataGridViewColumn[] { dataGridViewTextBoxColumn9, dataGridViewTextBoxColumn10, dataGridViewTextBoxColumn11, dataGridViewTextBoxColumn12 });
            dgvSanPhamI.Dock = DockStyle.Fill;
            dgvSanPhamI.Location = new Point(0, 0);
            dgvSanPhamI.Name = "dgvSanPhamI";
            dgvSanPhamI.ReadOnly = true;
            dgvSanPhamI.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvSanPhamI.Size = new Size(1192, 235);
            dgvSanPhamI.TabIndex = 0;
            dgvSanPhamI.SelectionChanged += DgvSanPhamI_SelectionChanged;
            //
            // dataGridViewTextBoxColumn9
            //
            dataGridViewTextBoxColumn9.HeaderText = "STT";
            dataGridViewTextBoxColumn9.Name = "dataGridViewTextBoxColumn9";
            dataGridViewTextBoxColumn9.ReadOnly = true;
            dataGridViewTextBoxColumn9.Width = 40;
            //
            // dataGridViewTextBoxColumn10
            //
            dataGridViewTextBoxColumn10.HeaderText = "Tên sản phẩm";
            dataGridViewTextBoxColumn10.Name = "dataGridViewTextBoxColumn10";
            dataGridViewTextBoxColumn10.ReadOnly = true;
            //
            // dataGridViewTextBoxColumn11
            //
            dataGridViewTextBoxColumn11.HeaderText = "Đơn vị hành chính";
            dataGridViewTextBoxColumn11.Name = "dataGridViewTextBoxColumn11";
            dataGridViewTextBoxColumn11.ReadOnly = true;
            //
            // dataGridViewTextBoxColumn12
            //
            dataGridViewTextBoxColumn12.HeaderText = "Trạng thái";
            dataGridViewTextBoxColumn12.Name = "dataGridViewTextBoxColumn12";
            dataGridViewTextBoxColumn12.ReadOnly = true;
            //
            // panelSanPhamIButtons
            //
            panelSanPhamIButtons.Controls.Add(btnThemSanPhamI);
            panelSanPhamIButtons.Controls.Add(btnSuaSanPhamI);
            panelSanPhamIButtons.Controls.Add(btnXoaSanPhamI);
            panelSanPhamIButtons.Dock = DockStyle.Bottom;
            panelSanPhamIButtons.Location = new Point(0, 235);
            panelSanPhamIButtons.Name = "panelSanPhamIButtons";
            panelSanPhamIButtons.Size = new Size(1192, 40);
            panelSanPhamIButtons.TabIndex = 1;
            //
            // btnThemSanPhamI
            //
            btnThemSanPhamI.BackColor = Color.FromArgb(40, 167, 69);
            btnThemSanPhamI.ForeColor = Color.White;
            btnThemSanPhamI.Location = new Point(13, 5);
            btnThemSanPhamI.Name = "btnThemSanPhamI";
            btnThemSanPhamI.Size = new Size(134, 30);
            btnThemSanPhamI.TabIndex = 0;
            btnThemSanPhamI.Text = "➕ Thêm";
            btnThemSanPhamI.UseVisualStyleBackColor = false;
            btnThemSanPhamI.Click += BtnThemSanPhamI_Click;
            //
            // btnSuaSanPhamI
            //
            btnSuaSanPhamI.BackColor = Color.FromArgb(255, 193, 7);
            btnSuaSanPhamI.ForeColor = Color.Black;
            btnSuaSanPhamI.Location = new Point(153, 5);
            btnSuaSanPhamI.Name = "btnSuaSanPhamI";
            btnSuaSanPhamI.Size = new Size(120, 30);
            btnSuaSanPhamI.TabIndex = 1;
            btnSuaSanPhamI.Text = "✏️ Sửa";
            btnSuaSanPhamI.UseVisualStyleBackColor = false;
            btnSuaSanPhamI.Click += BtnSuaSanPhamI_Click;
            //
            // btnXoaSanPhamI
            //
            btnXoaSanPhamI.BackColor = Color.FromArgb(220, 53, 69);
            btnXoaSanPhamI.ForeColor = Color.White;
            btnXoaSanPhamI.Location = new Point(279, 5);
            btnXoaSanPhamI.Name = "btnXoaSanPhamI";
            btnXoaSanPhamI.Size = new Size(120, 30);
            btnXoaSanPhamI.TabIndex = 2;
            btnXoaSanPhamI.Text = "❌ Xóa";
            btnXoaSanPhamI.UseVisualStyleBackColor = false;
            btnXoaSanPhamI.Click += BtnXoaSanPhamI_Click;
            //
            // groupBoxDacTinh
            //
            groupBoxDacTinh.Controls.Add(dgvDacTinhKyThuat);
            groupBoxDacTinh.Controls.Add(panelDacTinhButtons);
            groupBoxDacTinh.Dock = DockStyle.Fill;
            groupBoxDacTinh.Location = new Point(0, 0);
            groupBoxDacTinh.Name = "groupBoxDacTinh";
            groupBoxDacTinh.Size = new Size(1192, 191);
            groupBoxDacTinh.TabIndex = 0;
            groupBoxDacTinh.TabStop = false;
            groupBoxDacTinh.Text = "Đặc tính kỹ thuật";
            //
            // dgvDacTinhKyThuat
            //
            dgvDacTinhKyThuat.AllowUserToAddRows = false;
            dgvDacTinhKyThuat.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgvDacTinhKyThuat.Columns.AddRange(new DataGridViewColumn[] { dataGridViewTextBoxColumn13, dataGridViewTextBoxColumn14, dataGridViewTextBoxColumn15, dataGridViewTextBoxColumn16, dataGridViewTextBoxColumn17 });
            dgvDacTinhKyThuat.Dock = DockStyle.Fill;
            dgvDacTinhKyThuat.Location = new Point(3, 21);
            dgvDacTinhKyThuat.Name = "dgvDacTinhKyThuat";
            dgvDacTinhKyThuat.ReadOnly = true;
            dgvDacTinhKyThuat.Size = new Size(1186, 127);
            dgvDacTinhKyThuat.TabIndex = 0;
            //
            // dataGridViewTextBoxColumn13
            //
            dataGridViewTextBoxColumn13.HeaderText = "STT";
            dataGridViewTextBoxColumn13.Name = "dataGridViewTextBoxColumn13";
            dataGridViewTextBoxColumn13.ReadOnly = true;
            dataGridViewTextBoxColumn13.Width = 40;
            //
            // dataGridViewTextBoxColumn14
            //
            dataGridViewTextBoxColumn14.HeaderText = "Thông số";
            dataGridViewTextBoxColumn14.Name = "dataGridViewTextBoxColumn14";
            dataGridViewTextBoxColumn14.ReadOnly = true;
            //
            // dataGridViewTextBoxColumn15
            //
            dataGridViewTextBoxColumn15.HeaderText = "Giá trị";
            dataGridViewTextBoxColumn15.Name = "dataGridViewTextBoxColumn15";
            dataGridViewTextBoxColumn15.ReadOnly = true;
            //
            // dataGridViewTextBoxColumn16
            //
            dataGridViewTextBoxColumn16.HeaderText = "Đơn vị đo";
            dataGridViewTextBoxColumn16.Name = "dataGridViewTextBoxColumn16";
            dataGridViewTextBoxColumn16.ReadOnly = true;
            //
            // dataGridViewTextBoxColumn17
            //
            dataGridViewTextBoxColumn17.HeaderText = "Mô tả";
            dataGridViewTextBoxColumn17.Name = "dataGridViewTextBoxColumn17";
            dataGridViewTextBoxColumn17.ReadOnly = true;
            //
            // panelDacTinhButtons
            //
            panelDacTinhButtons.Controls.Add(btnThemDacTinh);
            panelDacTinhButtons.Controls.Add(btnSuaDacTinh);
            panelDacTinhButtons.Controls.Add(btnXoaDacTinh);
            panelDacTinhButtons.Dock = DockStyle.Bottom;
            panelDacTinhButtons.Location = new Point(3, 148);
            panelDacTinhButtons.Name = "panelDacTinhButtons";
            panelDacTinhButtons.Size = new Size(1186, 40);
            panelDacTinhButtons.TabIndex = 1;
            //
            // btnThemDacTinh
            //
            btnThemDacTinh.BackColor = Color.FromArgb(40, 167, 69);
            btnThemDacTinh.ForeColor = Color.White;
            btnThemDacTinh.Location = new Point(10, 5);
            btnThemDacTinh.Name = "btnThemDacTinh";
            btnThemDacTinh.Size = new Size(134, 30);
            btnThemDacTinh.TabIndex = 0;
            btnThemDacTinh.Text = "➕ Thêm đặc tính";
            btnThemDacTinh.UseVisualStyleBackColor = false;
            btnThemDacTinh.Click += BtnThemDacTinh_Click;
            //
            // btnSuaDacTinh
            //
            btnSuaDacTinh.BackColor = Color.FromArgb(255, 193, 7);
            btnSuaDacTinh.ForeColor = Color.Black;
            btnSuaDacTinh.Location = new Point(150, 5);
            btnSuaDacTinh.Name = "btnSuaDacTinh";
            btnSuaDacTinh.Size = new Size(120, 30);
            btnSuaDacTinh.TabIndex = 1;
            btnSuaDacTinh.Text = "✏️ Sửa đặc tính";
            btnSuaDacTinh.UseVisualStyleBackColor = false;
            btnSuaDacTinh.Click += BtnSuaDacTinh_Click;
            //
            // btnXoaDacTinh
            //
            btnXoaDacTinh.BackColor = Color.FromArgb(220, 53, 69);
            btnXoaDacTinh.ForeColor = Color.White;
            btnXoaDacTinh.Location = new Point(276, 5);
            btnXoaDacTinh.Name = "btnXoaDacTinh";
            btnXoaDacTinh.Size = new Size(120, 30);
            btnXoaDacTinh.TabIndex = 2;
            btnXoaDacTinh.Text = "❌ Xóa đặc tính";
            btnXoaDacTinh.UseVisualStyleBackColor = false;
            btnXoaDacTinh.Click += BtnXoaDacTinh_Click;
            //
            // tabSanPhamII
            //
            tabSanPhamII.Controls.Add(dgvSanPhamII);
            tabSanPhamII.Controls.Add(panelSanPhamIIButtons);
            tabSanPhamII.Location = new Point(4, 26);
            tabSanPhamII.Name = "tabSanPhamII";
            tabSanPhamII.Size = new Size(1192, 470);
            tabSanPhamII.TabIndex = 4;
            tabSanPhamII.Text = "📄 Sản phẩm Dạng II";
            tabSanPhamII.UseVisualStyleBackColor = true;
            //
            // dgvSanPhamII
            //
            dgvSanPhamII.AllowUserToAddRows = false;
            dgvSanPhamII.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgvSanPhamII.Columns.AddRange(new DataGridViewColumn[] { dataGridViewTextBoxColumn18, dataGridViewTextBoxColumn19, dataGridViewTextBoxColumn20 });
            dgvSanPhamII.Dock = DockStyle.Fill;
            dgvSanPhamII.Location = new Point(0, 0);
            dgvSanPhamII.Name = "dgvSanPhamII";
            dgvSanPhamII.ReadOnly = true;
            dgvSanPhamII.Size = new Size(1192, 430);
            dgvSanPhamII.TabIndex = 0;
            //
            // dataGridViewTextBoxColumn18
            //
            dataGridViewTextBoxColumn18.HeaderText = "STT";
            dataGridViewTextBoxColumn18.Name = "dataGridViewTextBoxColumn18";
            dataGridViewTextBoxColumn18.ReadOnly = true;
            dataGridViewTextBoxColumn18.Width = 40;
            //
            // dataGridViewTextBoxColumn19
            //
            dataGridViewTextBoxColumn19.HeaderText = "Tên sản phẩm";
            dataGridViewTextBoxColumn19.Name = "dataGridViewTextBoxColumn19";
            dataGridViewTextBoxColumn19.ReadOnly = true;
            //
            // dataGridViewTextBoxColumn20
            //
            dataGridViewTextBoxColumn20.HeaderText = "Loại sản phẩm";
            dataGridViewTextBoxColumn20.Name = "dataGridViewTextBoxColumn20";
            dataGridViewTextBoxColumn20.ReadOnly = true;
            //
            // panelSanPhamIIButtons
            //
            panelSanPhamIIButtons.Controls.Add(btnThemSanPhamII);
            panelSanPhamIIButtons.Controls.Add(btnSuaSanPhamII);
            panelSanPhamIIButtons.Controls.Add(btnXoaSanPhamII);
            panelSanPhamIIButtons.Dock = DockStyle.Bottom;
            panelSanPhamIIButtons.Location = new Point(0, 430);
            panelSanPhamIIButtons.Name = "panelSanPhamIIButtons";
            panelSanPhamIIButtons.Size = new Size(1192, 40);
            panelSanPhamIIButtons.TabIndex = 1;
            //
            // btnThemSanPhamII
            //
            btnThemSanPhamII.BackColor = Color.FromArgb(40, 167, 69);
            btnThemSanPhamII.ForeColor = Color.White;
            btnThemSanPhamII.Location = new Point(10, 5);
            btnThemSanPhamII.Name = "btnThemSanPhamII";
            btnThemSanPhamII.Size = new Size(100, 30);
            btnThemSanPhamII.TabIndex = 0;
            btnThemSanPhamII.Text = "➕ Thêm";
            btnThemSanPhamII.UseVisualStyleBackColor = false;
            btnThemSanPhamII.Click += BtnThemSanPhamII_Click;
            //
            // btnSuaSanPhamII
            //
            btnSuaSanPhamII.BackColor = Color.FromArgb(255, 193, 7);
            btnSuaSanPhamII.ForeColor = Color.Black;
            btnSuaSanPhamII.Location = new Point(120, 5);
            btnSuaSanPhamII.Name = "btnSuaSanPhamII";
            btnSuaSanPhamII.Size = new Size(100, 30);
            btnSuaSanPhamII.TabIndex = 1;
            btnSuaSanPhamII.Text = "✏️ Sửa";
            btnSuaSanPhamII.UseVisualStyleBackColor = false;
            btnSuaSanPhamII.Click += BtnSuaSanPhamII_Click;
            //
            // btnXoaSanPhamII
            //
            btnXoaSanPhamII.BackColor = Color.FromArgb(220, 53, 69);
            btnXoaSanPhamII.ForeColor = Color.White;
            btnXoaSanPhamII.Location = new Point(230, 5);
            btnXoaSanPhamII.Name = "btnXoaSanPhamII";
            btnXoaSanPhamII.Size = new Size(100, 30);
            btnXoaSanPhamII.TabIndex = 2;
            btnXoaSanPhamII.Text = "❌ Xóa";
            btnXoaSanPhamII.UseVisualStyleBackColor = false;
            btnXoaSanPhamII.Click += BtnXoaSanPhamII_Click;
            //
            // tabSanPhamIII
            //
            tabSanPhamIII.Controls.Add(dgvSanPhamIII);
            tabSanPhamIII.Controls.Add(panelSanPhamIIIButtons);
            tabSanPhamIII.Location = new Point(4, 26);
            tabSanPhamIII.Name = "tabSanPhamIII";
            tabSanPhamIII.Size = new Size(1192, 470);
            tabSanPhamIII.TabIndex = 5;
            tabSanPhamIII.Text = "📚 Sản phẩm Dạng III";
            tabSanPhamIII.UseVisualStyleBackColor = true;
            //
            // dgvSanPhamIII
            //
            dgvSanPhamIII.AllowUserToAddRows = false;
            dgvSanPhamIII.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgvSanPhamIII.Columns.AddRange(new DataGridViewColumn[] { dataGridViewTextBoxColumn21, dataGridViewTextBoxColumn22, dataGridViewTextBoxColumn23, dataGridViewTextBoxColumn24 });
            dgvSanPhamIII.Dock = DockStyle.Fill;
            dgvSanPhamIII.Location = new Point(0, 0);
            dgvSanPhamIII.Name = "dgvSanPhamIII";
            dgvSanPhamIII.ReadOnly = true;
            dgvSanPhamIII.Size = new Size(1192, 430);
            dgvSanPhamIII.TabIndex = 0;
            //
            // dataGridViewTextBoxColumn21
            //
            dataGridViewTextBoxColumn21.HeaderText = "STT";
            dataGridViewTextBoxColumn21.Name = "dataGridViewTextBoxColumn21";
            dataGridViewTextBoxColumn21.ReadOnly = true;
            dataGridViewTextBoxColumn21.Width = 40;
            //
            // dataGridViewTextBoxColumn22
            //
            dataGridViewTextBoxColumn22.HeaderText = "Tên sản phẩm";
            dataGridViewTextBoxColumn22.Name = "dataGridViewTextBoxColumn22";
            dataGridViewTextBoxColumn22.ReadOnly = true;
            //
            // dataGridViewTextBoxColumn23
            //
            dataGridViewTextBoxColumn23.HeaderText = "Loại sản phẩm";
            dataGridViewTextBoxColumn23.Name = "dataGridViewTextBoxColumn23";
            dataGridViewTextBoxColumn23.ReadOnly = true;
            //
            // dataGridViewTextBoxColumn24
            //
            dataGridViewTextBoxColumn24.HeaderText = "Nơi công bố";
            dataGridViewTextBoxColumn24.Name = "dataGridViewTextBoxColumn24";
            dataGridViewTextBoxColumn24.ReadOnly = true;
            //
            // panelSanPhamIIIButtons
            //
            panelSanPhamIIIButtons.Controls.Add(btnThemSanPhamIII);
            panelSanPhamIIIButtons.Controls.Add(btnSuaSanPhamIII);
            panelSanPhamIIIButtons.Controls.Add(btnXoaSanPhamIII);
            panelSanPhamIIIButtons.Dock = DockStyle.Bottom;
            panelSanPhamIIIButtons.Location = new Point(0, 430);
            panelSanPhamIIIButtons.Name = "panelSanPhamIIIButtons";
            panelSanPhamIIIButtons.Size = new Size(1192, 40);
            panelSanPhamIIIButtons.TabIndex = 1;
            //
            // btnThemSanPhamIII
            //
            btnThemSanPhamIII.BackColor = Color.FromArgb(40, 167, 69);
            btnThemSanPhamIII.ForeColor = Color.White;
            btnThemSanPhamIII.Location = new Point(10, 5);
            btnThemSanPhamIII.Name = "btnThemSanPhamIII";
            btnThemSanPhamIII.Size = new Size(100, 30);
            btnThemSanPhamIII.TabIndex = 0;
            btnThemSanPhamIII.Text = "➕ Thêm";
            btnThemSanPhamIII.UseVisualStyleBackColor = false;
            btnThemSanPhamIII.Click += BtnThemSanPhamIII_Click;
            //
            // btnSuaSanPhamIII
            //
            btnSuaSanPhamIII.BackColor = Color.FromArgb(255, 193, 7);
            btnSuaSanPhamIII.ForeColor = Color.Black;
            btnSuaSanPhamIII.Location = new Point(120, 5);
            btnSuaSanPhamIII.Name = "btnSuaSanPhamIII";
            btnSuaSanPhamIII.Size = new Size(100, 30);
            btnSuaSanPhamIII.TabIndex = 1;
            btnSuaSanPhamIII.Text = "✏️ Sửa";
            btnSuaSanPhamIII.UseVisualStyleBackColor = false;
            btnSuaSanPhamIII.Click += BtnSuaSanPhamIII_Click;
            //
            // btnXoaSanPhamIII
            //
            btnXoaSanPhamIII.BackColor = Color.FromArgb(220, 53, 69);
            btnXoaSanPhamIII.ForeColor = Color.White;
            btnXoaSanPhamIII.Location = new Point(230, 5);
            btnXoaSanPhamIII.Name = "btnXoaSanPhamIII";
            btnXoaSanPhamIII.Size = new Size(100, 30);
            btnXoaSanPhamIII.TabIndex = 2;
            btnXoaSanPhamIII.Text = "❌ Xóa";
            btnXoaSanPhamIII.UseVisualStyleBackColor = false;
            btnXoaSanPhamIII.Click += BtnXoaSanPhamIII_Click;
            //
            // frmDeTaiChiTiet
            //
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1200, 700);
            Controls.Add(tabControl);
            Controls.Add(panelHeader);
            Name = "frmDeTaiChiTiet";
            Text = "Chi tiết đề tài và sản phẩm";
            WindowState = FormWindowState.Maximized;
            Load += frmDeTaiChiTiet_Load;
            panelHeader.ResumeLayout(false);
            groupBoxInfo.ResumeLayout(false);
            groupBoxInfo.PerformLayout();
            tabControl.ResumeLayout(false);
            tabThanhVien.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)dgvThanhVien).EndInit();
            panelThanhVienButtons.ResumeLayout(false);
            tabDonVi.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)dgvDonVi).EndInit();
            panelDonViButtons.ResumeLayout(false);
            tabKinhPhi.ResumeLayout(false);
            groupBoxKinhPhi.ResumeLayout(false);
            groupBoxKinhPhi.PerformLayout();
            tabSanPhamI.ResumeLayout(false);
            splitContainerSanPhamI.Panel1.ResumeLayout(false);
            splitContainerSanPhamI.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)splitContainerSanPhamI).EndInit();
            splitContainerSanPhamI.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)dgvSanPhamI).EndInit();
            panelSanPhamIButtons.ResumeLayout(false);
            groupBoxDacTinh.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)dgvDacTinhKyThuat).EndInit();
            panelDacTinhButtons.ResumeLayout(false);
            tabSanPhamII.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)dgvSanPhamII).EndInit();
            panelSanPhamIIButtons.ResumeLayout(false);
            tabSanPhamIII.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)dgvSanPhamIII).EndInit();
            panelSanPhamIIIButtons.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        // Control declarations for Designer support
        private Panel panelHeader;
        private GroupBox groupBoxInfo;
        private Label lblDeTaiList;
        private ComboBox cmbDeTaiList;
        private Button btnThemDeTai;
        private Button btnSuaDeTai;
        private Button btnXoaDeTai;
        private Label lblMaDeTai;
        private TextBox txtMaDeTai;
        private Label lblTenDeTai;
        private TextBox txtTenDeTai;
        private Label lblMoTa;
        private TextBox txtMoTa;
        private Label lblLinhVuc;
        private TextBox txtLinhVuc;
        private Label lblCapQuanLy;
        private ComboBox cmbCapQuanLy;
        private Label lblThoiGianBatDau;
        private DateTimePicker dtpThoiGianBatDau;
        private Label lblThoiGianKetThuc;
        private DateTimePicker dtpThoiGianKetThuc;

        private TabControl tabControl;
        private TabPage tabThanhVien;
        private DataGridView dgvThanhVien;
        private Panel panelThanhVienButtons;
        private Button btnThemThanhVien;
        private Button btnXoaThanhVien;

        private TabPage tabDonVi;
        private DataGridView dgvDonVi;
        private Panel panelDonViButtons;
        private Button btnThemDonVi;
        private Button btnSuaDonVi;
        private Button btnXoaDonVi;

        private TabPage tabKinhPhi;
        private GroupBox groupBoxKinhPhi;
        private Label lblKinhPhiNganSach;
        private TextBox txtKinhPhiNganSach;
        private Label lblKinhPhiKhac;
        private TextBox txtKinhPhiKhac;
        private Label lblTongKinhPhi;
        private TextBox txtTongKinhPhi;

        private TabPage tabSanPhamI;
        private SplitContainer splitContainerSanPhamI;
        private DataGridView dgvSanPhamI;
        private Panel panelSanPhamIButtons;
        private Button btnThemSanPhamI;
        private Button btnSuaSanPhamI;
        private Button btnXoaSanPhamI;
        private GroupBox groupBoxDacTinh;
        private DataGridView dgvDacTinhKyThuat;
        private Panel panelDacTinhButtons;
        private Button btnThemDacTinh;
        private Button btnSuaDacTinh;
        private Button btnXoaDacTinh;

        private TabPage tabSanPhamII;
        private DataGridView dgvSanPhamII;
        private Panel panelSanPhamIIButtons;
        private Button btnThemSanPhamII;
        private Button btnSuaSanPhamII;
        private Button btnXoaSanPhamII;

        private TabPage tabSanPhamIII;
        private DataGridView dgvSanPhamIII;
        private Panel panelSanPhamIIIButtons;
        private Button btnThemSanPhamIII;
        private Button btnSuaSanPhamIII;
        private Button btnXoaSanPhamIII;
        private Button btnLuu;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn1;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn2;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn3;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn4;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn5;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn6;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn7;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn8;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn9;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn10;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn11;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn12;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn13;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn14;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn15;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn16;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn17;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn18;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn19;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn20;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn21;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn22;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn23;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn24;
    }
}
